import "@testing-library/jest-dom/vitest";
import { vi } from "vitest";

// Polyfill localStorage for Node test environment
if (typeof globalThis.localStorage === "undefined") {
  const store = new Map<string, string>();
  globalThis.localStorage = {
    getItem(key: string) {
      return store.has(key) ? store.get(key)! : null;
    },
    setItem(key: string, value: string) {
      store.set(key, String(value));
    },
    removeItem(key: string) {
      store.delete(key);
    },
    clear() {
      store.clear();
    },
    key(index: number) {
      return Array.from(store.keys())[index] ?? null;
    },
    get length() {
      return store.size;
    },
  } as unknown as Storage;
}

// Ensure crypto.randomUUID exists in Node test env
if (typeof globalThis.crypto === "undefined") {
  // @ts-ignore
  globalThis.crypto = {} as Crypto;
}
if (typeof (globalThis.crypto as any).randomUUID !== "function") {
  // simple fallback for tests only (type loosened for Vitest env)
  (globalThis.crypto as any).randomUUID = function randomUUID(): string {
    // RFC4122 v4 UUID string
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
      const r = (Math.random() * 16) | 0;
      const v = c === "x" ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  };
}

// jsdom not implemented APIs
if (typeof (globalThis as any).scrollTo !== "function") {
  (globalThis as any).scrollTo = () => {};
}
if (typeof document.hasFocus !== "function") {
  // @ts-ignore
  document.hasFocus = () => false;
}

// Provide a safe default fetch to prevent real network calls leaking from tests that forgot to stub.
// Individual tests can override with vi.stubGlobal("fetch", ...)
if (typeof (globalThis as any).fetch !== "function") {
  (globalThis as any).fetch = async () =>
    new Response(JSON.stringify({ error: { message: "unmocked fetch" } }), {
      status: 501,
      headers: { "Content-Type": "application/json", "X-Trace-Id": "tid-unmocked" },
    });
}

/**
 * 全局 rAF polyfill：映射为 setTimeout(cb, 16)
 * - Vitest fake timers 下，通过 advanceTimersByTimeAsync 可可靠推进 rAF
 */
if (typeof globalThis.requestAnimationFrame !== "function") {
  // @ts-ignore
  globalThis.requestAnimationFrame = (cb: FrameRequestCallback) =>
    (setTimeout(() => cb(performance.now()), 16) as unknown as number);
}
if (typeof globalThis.cancelAnimationFrame !== "function") {
  // @ts-ignore
  globalThis.cancelAnimationFrame = (h: number) => clearTimeout(h as unknown as any);
}

/**
 * 统一推进工具：在测试中可调用 globalThis.__advanceAndFlush__(ms)
 * - 分段推进 timers
 * - flush 微任务
 * - 一个空 act 结算 React 更新
 */
declare global {
  // eslint-disable-next-line no-var
  var __advanceAndFlush__: (ms?: number) => Promise<void>;
}

(async () => {
  const { vi } = await import("vitest");
  const { act } = await import("@testing-library/react");
  (globalThis as any).__advanceAndFlush__ = async (ms = 0) => {
    await act(async () => {
      if (ms > 0) {
        await vi.advanceTimersByTimeAsync(ms);
      }
      await vi.runOnlyPendingTimersAsync();
    });
    // flush micro & a macrotask(0)
    await Promise.resolve();
    await new Promise<void>((r) => setTimeout(r, 0));
    await act(async () => {});
  };
})();